{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A3 สัญญาเช่า สำหรับตัดแผนที่</title>
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="{% static 'plugin_leaflet\Leaflet-Switch-Scale-Control\leaflet-switch-scale-control.js' %}"></script>
    <script src="{% static 'assets/js/slms.js' %}"></script>
    <!-- // Include turf.js in your HTML -->
    <script src="https://cdn.jsdelivr.net/npm/@turf/turf@6.5.0"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/proj4js/2.7.5/proj4.js"></script> -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/proj4js/2.10.0/proj4.js" integrity="sha512-e3rsOu6v8lmVnZylXpOq3DO/UxrCgoEMqosQxGygrgHlves9HTwQzVQ/dLO+nwSbOSAecjRD7Y/c4onmiBVo6w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- End Proj4 -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Sarabun:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
    </style>
    <script>
        $(document).ready(function() {
            // Print functionality
            $('#printButton').click(function() {
                window.print();
            });
            
            
            // Drag functionality
            interact('.draggable')
            .draggable({
                inertia: true,
                modifiers: [
                interact.modifiers.restrictRect({
                    restriction: 'parent',
                    endOnly: true
                })
                ],
                autoScroll: true,
                listeners: {
                    move: dragMoveListener,
                    end: function(event) {
                        var target = event.target;
                        var x = parseFloat(target.getAttribute('data-x')) || 0;
                        var y = parseFloat(target.getAttribute('data-y')) || 0;
                        console.log('Element ID:', target.id, 'X:', x, 'Y:', y);
                    }
                }
            });
        });
    
        function dragMoveListener (event) {
            var target = event.target;
            var x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
            var y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;
            target.style.transform = 'translate(' + x + 'px, ' + y + 'px)';
            target.setAttribute('data-x', x);
            target.setAttribute('data-y', y);
        }
    </script>
    
    <style>
        body {
            background-color: #ffffff;
            font-family: "Sarabun", sans-serif;
            font-weight: 200;
            font-style: normal;
        }
        page[size="A3"][layout="landscape"] {
            position: relative;
            display: block;
            width: 42cm;
            height: 29.6cm;
            margin: 1cm auto;
            background: white;
        }
        .draggable {
            /* font color */
            color: #010101;
            /* font size */
            font-size: 13px;
            width: auto;
            height: auto;
            position: absolute;
            background-color: transparent;
            z-index: 99;
        }
        @media print {
            body, page[size="A3"][layout="landscape"] {
                margin: 0;
                page-break-after: avoid;
                page-break-before: avoid;
            }
            #printButton {
                display: none;
            }
            #scale_dropdown {
                display: none;
            }
        }
        .text_map1mm {
            font-family: "Sarabun", sans-serif;
            font-weight: 100;
            font-style: normal;
            font-size: 8px;
            color: #010101;
            text-align: center;
            line-height: 7px;
            display: flex;
            align-items: center;  /* Aligns items vertically in the center */
            justify-content: center;  /* Aligns items horizontally in the center */
            height: 100%;  /* Ensure the flex container takes full height of its parent */
        }
        .text_map2mm {
            font-family: "Sarabun", sans-serif;
            font-weight: 100;
            font-style: normal;
            font-size:12px;
            color: #010101;
            text-align: center;
            line-height: 8px;
            display: flex;
            align-items: center;  /* Aligns items vertically in the center */
            justify-content: center;  /* Aligns items horizontally in the center */
            height: 100%;  /* Ensure the flex container takes full height of its parent */
        }
        .leaflet-container {
            background: #ffffff;
            outline: 0;
        }
        #scale_dropdown {
            position: absolute;
            top: 40px;
            right: 10px;
            z-index: 999;
        }
        #printButton {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 999;
        }
    </style>
</head>
<body>
<button id="printButton">Print this page</button>
<!-- dropdown scale_dropdown -->
<div id="scale_dropdown">
    <select id="scale_select_dropdown" name="scale_select">
        <option value="1">Select Scale</option>
        <option value="100">1:100</option>
        <option value="150">1:150</option>
        <option value="200">1:200</option>
        <option value="250">1:250</option>
        <option value="300">1:300</option>
        <option value="350">1:350</option>
        <option value="400">1:400</option>
        <option value="450">1:450</option>
        <option value="500">1:500</option>
        <option value="550">1:550</option>
        <option value="600">1:600</option>
        <option value="650">1:650</option>
        <option value="700">1:700</option>
        <option value="750">1:750</option>
        <option value="800">1:800</option>
        <option value="850">1:850</option>
        <option value="900">1:900</option>
        <option value="950">1:950</option>
        <option value="1000">1:1000</option>
        <option value="1250">1:1250</option>
        <option value="2000">1:2000</option>
        <option value="2500">1:2500</option>
        <option value="3000">1:3000</option>
        <option value="3500">1:3500</option>
        <option value="4000">1:4000</option>
        <option value="4500">1:4500</option>
        <option value="5000">1:5000</option>
        <option value="5500">1:5500</option>
        <option value="6000">1:6000</option>
        <option value="6500">1:6500</option>
        <option value="7000">1:7000</option>
        <!-- input scale custom -->
        <input type="number" id="scale_custom" name="scale_custom" placeholder="Custom Scale" style="width: 100px;">
        
    </select>
</div>

<page size="A3" layout="landscape">
    <img src="{% static 'assets/layout/a3.jpg' %}" width="100%" height="100%">
    <!-- ชื่อผู้เช่า -->
    <div id="name_rent" class="draggable" style="top:119px; left:484px;">
        <p>นายผดุงศักดิ์ วิริยพิสุทธิ์</p>
    </div>
    <!-- ประเภทสัญญา -->
    <div id="type" class="draggable" style="top:119px; left:896px;">
        <p>ที่ดิน</p>
    </div>
    <!-- ชื่ออัตรา -->
    <div id="name_type" class="draggable" style="top:119px; left:1014px;">
        <p>ที่ดินรายเดือน</p>
    </div>
    <!-- หมายเลขที่ดิน -->
    <div id="number_rent" class="draggable" style="top:146px; left:548px;">
        <p>50</p>
    </div>
    <!-- ภาษาตำบล -->
    <div id="name_tumbon" class="draggable" style="top:144px; left:835px;">
        <p>แขวงแสมดำ เขตบางขุนเทียน</p>
    </div>
    <!-- ตำบล/แขวง -->
    <div id="tumbon" class="draggable" style="top:172px; left:516px;">
        <p>บางขุนเทียน</p>
    </div>
     <!-- อำเภอ/เขต -->
     <div id="distric" class="draggable" style="top:172px; left:736px;">
        <p>บางขุนเทียน</p>
    </div>
    <!-- จังหวัด -->
    <div id="province" class="draggable" style="top:172px; left:948px;">
        <p>กรุงเทพมหานคร</p>
    </div>
    <!-- no_deed -->
    <div id="no_land" class="draggable" style="top:198px; left:564px;">
        <p>1</p>
    </div>
    <!-- no_deed -->
    <div id="deed_number" class="draggable" style="top:198px; left:860px;">
        <p>6653</p>
    </div>
    <!-- พื้นที่เช่า -->
    <div id="area" class="draggable" style="top:224px; left:558px;">
        <p>20.00</p>
    </div>
    <!-- วันรังวัด -->
    <div id="date_survey" class="draggable" style="top:225px; left:798px;">
        <p>29 กันยายน 2566</p>
    </div>
    <!-- มาตราส่วน -->
    <div id="scale_map" class="draggable" style="top:753.5px; left:289px; font-size: 11px;">
        <p>1,500</p>
    </div>
    <!-- ปักหลักเขต -->
    <div id="marker_pin" class="draggable" style="top:731px; left:681px;">
        <p>2</p>
    </div>
    <!-- หมายเหตุ -->
    <div id="remark" class="draggable" style="top:761px; left:408px;">
        <p>ในที่ดินแปลงนี้มี  นายผดุงศักดิ์ วิริยพิสุทธิ์  เป็นผู้เช่า</p>
    </div>
    <div id="map" style="width: 650px; height: 445px; top:300px; left:130px; z-index: 99; position: absolute;" class="leaflet-container"></div>
    <div id="map2" style="width: 650px; height: 445px; top:300px; left:835px; z-index: 99; position: absolute;"></div>
    <div id="map3" style="width: 244px; height: 272px; top:815.3px; left:90px; z-index: 99; position: absolute;"></div>
    
</page>




</body>
<script>
    // http://localhost:8001/editor_page/130/ split by /
    var url = window.location.href;
    var urlSplit = url.split('/');
    var id = urlSplit[urlSplit.length - 2];
    // it to float
    id_timeline = parseFloat(id);
    console.log(id);
    // Define the SVG with CSS to load external font
    var fontBase64 = 'data:application/font-woff;base64,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';

    $(document).ready(function() {
                
        var map = L.map('map',{
            zoomDelta: 0.1, // ความละเอียดการ zoom
            zoomSnap: 0.1, // ความละเอียดการ zoom
        }).setView([13.736717, 100.523186], 10);
        // map remove zoom control
        map.removeControl(map.zoomControl);
        // map remove attribution
        map.removeControl(map.attributionControl);
        // L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        //     maxZoom: 19,
        // }).addTo(map);

        

        proj4.defs('DOL INDIAN 1975 ZONE47N', "+proj=utm +zone=47 +ellps=evrst30 +towgs84=204.5,837.9,294.8,0,0,0,0 +units=m +no_defs +type=crs");
        proj4.defs('DOL INDIAN 1975 ZONE48N', "+proj=utm +zone=48 +ellps=evrst30 +towgs84=204.5,837.9,294.8,0,0,0,0 +units=m +no_defs +type=crs");
        proj4.defs('WGS84 UTM ZONE47N', "+title=WGS 1984 / UTM zone 47N+proj=utm +zone=47 +ellps=WGS84 +datum=WGS84 +units=m +no_defs");
        proj4.defs('WGS84 UTM ZONE48N', "+title=WGS 1984 / UTM zone 48N+proj=utm +zone=48 +ellps=WGS84 +datum=WGS84 +units=m +no_defs");
        proj4.defs('WGS84', "+title=longlat / WGS84+proj=longlat +datum=WGS84 +no_defs +units=degrees");

        // stype polygon
        var polygonStyle = {
            "color": "#000000", // black color
            "weight": 1,
            "opacity": 1,
            "fillOpacity": 0, // Adjust fillOpacity as needed
        };
        var polygonStyle1_offset = {
            "color": "#FFFF2B", // Yellow color
            "weight": 2,
            "opacity": 1,
            "fillOpacity": 0 // Adjust fillOpacity as needed
        };

        var number_shp =L.layerGroup().addTo(map);
        // console.log(number_shp.getLayers().length);
        
        // ajax แสดงรายละเอียด กรรมสิทธิ์
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/ajax_layout_render_detail/',
            data: {
                'id_timeline': id_timeline,
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                // ชื่อผู้เช่า
                $('#name_rent').html('<p>' + response['name_rent'] + '</p>');
                // ประเภทสัญญา
                $('#type').html('<p>' + response['prom_type'] + '</p>');
                // ชื่ออัตรา
                $('#name_type').html('<p>' + response['rate_type'] + '</p>');
                // หมายเลขที่ดิน
                $('#number_rent').html('<p>' + response['number_parcel_rent'] + '</p>');
                // ภาษาตำบล
                $('#name_tumbon').html('<p>' + response['talk_tunbon'] + '</p>');
                // ตำบล/แขวง
                $('#tumbon').html('<p>' + response['tumbon'] + '</p>');
                // อำเภอ/เขต
                $('#distric').html('<p>' + response['amphoe'] + '</p>');
                // จังหวัด
                $('#province').html('<p>' + response['province'] + '</p>');
                // no_deed
                $('#no_land').html('<p>' + response['land_no'] + '</p>');
                // no_deed
                $('#deed_number').html('<p>' + response['deed_no'] + '</p>');
                // พื้นที่เช่า
                $('#area').html('<p>' + parseFloat(response['area_rent_sqwa']).toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }) + '</p>');
                // วันรังวัด
                $('#date_survey').html('<p>' + response['sv_datetime'] + '</p>');
                // remark
                $('#remark').html('<p>ในที่ดินแปลงนี้มี  '+ response['name_rent'] +'  เป็นผู้เช่า</p>');

            }
        });
        // End ajax แสดงรายละเอียด กรรมสิทธิ์


        //  create layer group
        var shp_rent;
        shp_rent = L.layerGroup().addTo(map);
        var center_parcel_rent;
        // ajax แสดงรูปแปลงที่ดิน 
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/ajax_layout_render_parcel/',
            data: {
                'id_timeline': id_timeline,
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                shp_rent = L.geoJSON(response, {
                    style: polygonStyle,
                    onEachFeature: function (feature, layer) {
                        disoffset = ((0.04*scale)/100)*-1;
                        // console.log(disoffset);
                        // offset turf
                        var buffered = turf.buffer(feature, disoffset, {units: 'meters'});
                        // Create a Leaflet geoJSON layer for the buffered polygon and style it
                        var offsetPolygon = L.geoJSON(buffered).addTo(map).setStyle(polygonStyle1_offset);
                        // Calculate the centroid of the original feature
                        center_parcel_rent = turf.centroid(feature);
                        // center_geojson_rent = center.geometry.coordinates;
                    }
                });
                // add to map
                shp_rent.addTo(map);
                // console.log('Center GeoJSON Rent after processing:', center_parcel_rent);
                

                
            }
        });
        // End ajax แสดงรูปแปลงที่ดิน

        // console.log('shp:', shp_rent);
        // show scale
        var polygon_scale;
        var scale;
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/scale_map/',
            data: {
                'id_time_line': id_timeline,
                'type_query': 'get',
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                scale_nondecimal = parseFloat(response).toFixed(0);
                // no_deed
                $('#scale_map').html('<p>' + scale_nondecimal + '</p>');
                // change scale dropdown
                $('#scale_select_dropdown').val(scale_nondecimal);
                scale = response;
                // Delay to ensure the center_geojson_rent is updated
                setTimeout(function() {
                    // Calculate distances
                    var distance_mm_hor = scale * 172; // คือขนาดความกว้างของแผนที่แนวนอน 172 mm
                    var distance_m_hor = distance_mm_hor / 1000; // Convert mm to meters
                    var distance_mm_ver = scale * 118; // คือขนาดความสูงของแผนที่แนวตั้ง 118 mm
                    var distance_m_ver = distance_mm_ver / 1000; // Convert mm to meters

                    // Get center of shape rent layer
                    var center_shp_rent = shp_rent.getBounds().getCenter();
                    // Determine UTM zone based on longitude
                    var utm_zone = center_shp_rent.lng < 102 ? 'WGS84 UTM ZONE47N' : 'WGS84 UTM ZONE48N';
                    var converted_en = proj4('WGS84', utm_zone, [center_shp_rent.lng, center_shp_rent.lat]);
                    var center_e_con = converted_en[0];
                    var center_n_con = converted_en[1];
                    // Remove previous polygon layer if exists
                    if (polygon_scale) {
                        map.removeLayer(polygon_scale);
                    }

                    // Calculate line lengths and coordinates for points
                    var lineLengthHor = distance_m_hor / 2;
                    var lineLengthVer = distance_m_ver / 2;
                    var eastPoint = proj4(utm_zone, 'WGS84', [center_e_con + lineLengthHor, center_n_con - lineLengthVer]);
                    var westPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con + lineLengthVer]);
                    var northPoint = proj4(utm_zone, 'WGS84', [center_e_con+ lineLengthHor, center_n_con + lineLengthVer]);
                    var southPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con - lineLengthVer]);
                    // Draw new polygon
                    polygon_scale = L.polygon([
                        [northPoint[1], northPoint[0]],
                        [eastPoint[1], eastPoint[0]],
                        [southPoint[1], southPoint[0]],
                        [westPoint[1], westPoint[0]]
                    ], {
                        // color transparent
                        color: 'transparent',
                        // color: 'red'
                    }).addTo(map);

                    // Fit bounds to new polygon
                    map.fitBounds(polygon_scale.getBounds());
                }, 1000);
                // console.log(center_geojson_rent);
                // console.log(shp_rent_geojson);
                // shp_rent_geojson[0].features[0].
                
                
            }
        });
        // End show scale

        // Show ขีดเขตแยก
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/side_plot_lines/',
            data: {
                'id_time_line': id_timeline,
                'type_query': 'show',
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                // get geojson
                var geojson = response;
                // style
                var style = {
                    "color": "#000000", // black color
                    "weight": 1,
                    "opacity": 1,
                    "fillOpacity": 0, // Adjust fillOpacity as needed
                };
                // add to map
                L.geoJson(response, {
                    style: style,
                    onEachFeature: function (feature, layer) {
                        layer.properties = feature.properties;
                    }
                }).addTo(map);
                
                
            }
        });
        // END Show ขีดเขตแยก

        // change scale
        // Declare polygon outside to have global scope
        var polygon_scale;
        $('#scale_select_dropdown').change(function() {
            var scale = $(this).val();
            console.log(scale);
            // if scale >= 1000, add commas
            var scale_text = scale >= 1000 ? scale.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") : scale;
            
            // Change scale display
            $('#scale_map').html('<p>' + scale_text + '</p>');

            // Calculate distances
            var distance_mm_hor = scale * 172; // คือขนาดความกว้างของแผนที่แนวนอน 172 mm
            var distance_m_hor = distance_mm_hor / 1000; // Convert mm to meters
            var distance_mm_ver = scale * 118; // คือขนาดความสูงของแผนที่แนวตั้ง 118 mm
            var distance_m_ver = distance_mm_ver / 1000; // Convert mm to meters

            // Get center of shape rent layer
            var center_shp_rent = shp_rent.getBounds().getCenter();

            // Determine UTM zone based on longitude
            var utm_zone = center_shp_rent.lng < 102 ? 'WGS84 UTM ZONE47N' : 'WGS84 UTM ZONE48N';
            var converted_en = proj4('WGS84', utm_zone, [center_shp_rent.lng, center_shp_rent.lat]);
            var center_e_con = converted_en[0];
            var center_n_con = converted_en[1];

            // Remove previous polygon layer if exists
            if (polygon_scale) {
                map.removeLayer(polygon_scale);
            }

            // Calculate line lengths and coordinates for points
            var lineLengthHor = distance_m_hor / 2;
            var lineLengthVer = distance_m_ver / 2;
            var eastPoint = proj4(utm_zone, 'WGS84', [center_e_con + lineLengthHor, center_n_con - lineLengthVer]);
            var westPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con + lineLengthVer]);
            var northPoint = proj4(utm_zone, 'WGS84', [center_e_con+ lineLengthHor, center_n_con + lineLengthVer]);
            var southPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con - lineLengthVer]);

            // Draw new polygon
            polygon_scale = L.polygon([
                [northPoint[1], northPoint[0]],
                [eastPoint[1], eastPoint[0]],
                [southPoint[1], southPoint[0]],
                [westPoint[1], westPoint[0]]
            ], {
                // color transparent
                color: 'transparent',
                // color: 'red'
            }).addTo(map);

            // Fit bounds to new polygon
            map.fitBounds(polygon_scale.getBounds());
            // get zoom level
            var zoom = map.getZoom();
            console.log(zoom);

            
        });

        // Define a layer group for side plot lines
        var pin_marker_rents = L.layerGroup().addTo(map);
        // Define a layer group for side plot lines
        var pin_marker_rents_label = L.layerGroup().addTo(map);
        

        var topPane = map.createPane('pane_pin_boundary_rent'); // หมุดหลักเขต
        topPane.style.zIndex = 651;  // Higher than the overlay pane which is 400 by default

        // Define custom icon
        var url_icon = '/static/assets/svg/point.svg';
        var myIcon = L.icon({
            iconUrl: url_icon,
            iconSize: [10, 10], // ขนาดของไอคอน
            iconAnchor: [5, 5], // จุดกึ่งกลางไอคอนจะอยู่ที่จุดกึ่งกลางของไอคอน (ครึ่งของ 18)
            popupAnchor: [0, -9] // ปรับป็อปอัพให้อยู่ด้านบนของหมุด
        });  
        // โหลดหมุดเขต
        $.ajax({
            headers: { "X-CSRFToken": "{{ csrf_token }}" }, // Ensure CSRF token is sent with request
            url: '/ajax_pin_rents/', // URL to the Django view
            data: {
                'id_time_line': id_timeline, // Variable id_time_line should be defined in your script
                'type_query': 'get_pin_rents',
            },
            type: 'POST',
            success: function (response) {
                var geojsonLayer = L.geoJson(response['point_rents'], {
                        pointToLayer: function(feature, latlng) {
                            return L.marker(latlng, {icon: myIcon, pane: 'pane_pin_boundary_rent'}); // Use the top pane
                        },
                        onEachFeature: function (feature, layer) {
                            // add property to layer
                            layer.properties = feature.properties;
                            // Add layer to layer group
                            pin_marker_rents.addLayer(layer);
                            // Add layer to map
                            layer.addTo(map);
                        }
                    });
                // Create a GeoJSON layer from the response with custom icon for each marker
                var geojsonLayer_label = L.geoJson(response['point_label'], {
                        onEachFeature: function (feature, layer) {
                            // split by \n
                            var contents = feature.properties.contents.split('\n');
                            // count contents
                            var count_contents = contents.length;
                            if (count_contents > 1) {
                                // ข้อความ 2 บรรทัด
                                var textIcon = L.divIcon({
                                    className: 'text_map1mm',
                                    html: '<div style="transform: rotate(0deg);">' + contents[0] + '<br>' + contents[1] + '</div>',
                                    iconSize: [100, 40] // Optional, depending on the need to adjust the icon size
                                });
                            } else {
                                // ข้อความ 1 บรรทัด
                                var textIcon = L.divIcon({
                                    className: 'text_map1mm',
                                    html: '<div style="transform: rotate(0deg);">' + feature.properties.contents + '</div>',
                                    iconSize: [100, 30] // Optional, depending on the need to adjust the icon size
                                });
                            }

                            // Set the custom icon to the layer
                            if (layer instanceof L.Marker) {
                                layer.setIcon(textIcon);
                                layer.options.pane = 'pane_pin_boundary_rent';
                            } else if (layer instanceof L.Path) {
                                // Optional: Handle cases where the layer is not a marker but another type like polygons
                                // This is just in case your GeoJSON contains other types of geometries
                            }

                            // Add the feature's properties to the layer
                            layer.properties = feature.properties;

                            // Add layer to layer group and map
                            pin_marker_rents_label.addLayer(layer);
                            layer.addTo(map);
                        }
                    });
            }
        });
        // End โหลดหมุดเขต

        // วงหมุดเลขแปลง
        var circle_parcel;
        var topPane = map.createPane('pane_circle_parcel'); // หมุดหลักเขต
        topPane.style.zIndex = 651;  // Higher than the overlay pane which is 400 by default
        // ajax get circle parcel
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/ajax_parcel_number/',
            data: {
                'id_time_line': id_timeline,
                'type': 'no_parcel',
                'type_query': 'get',
                'scale_map': scale,
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                // get geojson
                var geojson = response;
                // add geojson to map
                L.geoJson(geojson, {
                    onEachFeature: function (feature, layer) {
                        // pane
                        layer.options.pane = 'pane_circle_parcel';
                        // add property to layer
                        layer.properties = feature.properties;
                        console.log(layer.properties.content);
                        var svgContent = `
                        <svg height="100" width="100" xmlns="http://www.w3.org/2000/svg">
                            <style>
                                @font-face {
                                    font-family: 'THSarabun';
                                    src: url(${fontBase64}) format('woff');
                                }
                                text {
                                    font-family: 'THSarabun';
                                    font-size: 40px;
                                    fill: black;
                                    dominant-baseline: middle;
                                    text-anchor: middle;
                                }
                            </style>
                            <circle r="45" cx="50" cy="50" fill="white" stroke="black" stroke-width="3" />
                            <text x="50%" y="50%">${layer.properties.content}</text>
                        </svg>`;
                        // Encode SVG content as base64
                        var svgDataUri = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgContent)));
                        // Define custom icon using the SVG Data URI
                        var myIcon = L.icon({
                            iconUrl: svgDataUri,
                            iconSize: [35, 35], // Size of the icon
                            iconAnchor: [17.5, 17.5], // Center of the icon
                            popupAnchor: [0, -30] // Popup anchor point
                        });
                        // add to map
                        layer.setIcon(myIcon);
                        layer.addTo(map);
                        
                    }
                    
                });


            }
        });
        
        // End วงหมุดเลขแปลง

        // ส่วนข้อความบนแผนที่
        // create pane
        map.createPane('pane_text_map');
        map.getPane('pane_text_map').style.zIndex = 951;
        // create layer group
        var text_map_group = L.layerGroup().addTo(map);
        // ajax get text map
        // ajax
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/case_text_map/',
            data: {
                'id_time_line': id_timeline,
                'case': 'show_text_map',
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                // oneach feature
                L.geoJson(response, {
                    pane: 'pane_text_map',
                    onEachFeature: function (feature, layer) {
                        // font size
                        var fontSize = feature.properties.size/2;
                        // สร้าง text icon จากข้อความที่ผู้ใช้ป้อน
                        var textIcon = L.divIcon({
                            className: 'text_map2mm',
                            html: `
                                <style>
                                    @font-face {
                                        font-family: 'THSarabun';
                                        src: url(${fontBase64}) format('woff');
                                    }
                                </style>
                                <div style="font-size: ${fontSize}px; transform: rotate(${feature.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun';">
                                    ${feature.properties.content}
                                </div>
                            `,
                            iconSize: [100, 30]
                        });
                        // สร้าง marker ด้วย text icon
                        var textMarker = L.marker([feature.geometry.coordinates[1], feature.geometry.coordinates[0]], {
                            icon: textIcon,
                            pane: 'pane_text_map'
                        });
                        // Initialize properties object if it doesn't exist
                        textMarker.properties = {};

                        // เพิ่ม properties จาก feature ลงใน marker
                        textMarker.properties.id = feature.properties.id;
                        textMarker.properties.content = feature.properties.content;
                        textMarker.properties.size = feature.properties.size;
                        textMarker.properties.rotate = feature.properties.rotate;
                        textMarker.properties.type = feature.properties.type;

                        // เพิ่ม marker ลงใน text_map_group
                        text_map_group.addLayer(textMarker);
                        text_map_group.addTo(map);
                    }
                });
            }
        });
        // End ส่วนข้อความบนแผนที่

        
        // Get and log the center of the shp_rent layer
        if (typeof shp_rent !== 'undefined' && shp_rent.getBounds) {
            var center_shp_rent = shp_rent.getBounds().getCenter();
            console.log(center_shp_rent);
        } else {
            console.error("shp_rent layer is not defined or does not have getBounds method.");
        }


        
        
    });
    
    //    End Map ......................................................................................

    //    Map 2 ......................................................................................

        var map = L.map('map',{
            zoomDelta: 0.1, // ความละเอียดการ zoom
            zoomSnap: 0.1, // ความละเอียดการ zoom
        }).setView([13.736717, 100.523186], 10);
        // map remove zoom control
        map.removeControl(map.zoomControl);
        // map remove attribution
        map.removeControl(map.attributionControl);
        // L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        //     maxZoom: 19,
        // }).addTo(map);

        

        proj4.defs('DOL INDIAN 1975 ZONE47N', "+proj=utm +zone=47 +ellps=evrst30 +towgs84=204.5,837.9,294.8,0,0,0,0 +units=m +no_defs +type=crs");
        proj4.defs('DOL INDIAN 1975 ZONE48N', "+proj=utm +zone=48 +ellps=evrst30 +towgs84=204.5,837.9,294.8,0,0,0,0 +units=m +no_defs +type=crs");
        proj4.defs('WGS84 UTM ZONE47N', "+title=WGS 1984 / UTM zone 47N+proj=utm +zone=47 +ellps=WGS84 +datum=WGS84 +units=m +no_defs");
        proj4.defs('WGS84 UTM ZONE48N', "+title=WGS 1984 / UTM zone 48N+proj=utm +zone=48 +ellps=WGS84 +datum=WGS84 +units=m +no_defs");
        proj4.defs('WGS84', "+title=longlat / WGS84+proj=longlat +datum=WGS84 +no_defs +units=degrees");

        // stype polygon
        var polygonStyle = {
            "color": "#000000", // black color
            "weight": 1,
            "opacity": 1,
            "fillOpacity": 0, // Adjust fillOpacity as needed
        };
        var polygonStyle1_offset = {
            "color": "#FFFF2B", // Yellow color
            "weight": 2,
            "opacity": 1,
            "fillOpacity": 0 // Adjust fillOpacity as needed
        };

        var number_shp =L.layerGroup().addTo(map);
        // console.log(number_shp.getLayers().length);
        





        //    Map 2 ......................................................................................

        // map2 = L.map('map2', {
        //     // remove Zoom Control
        //     zoomControl: false,
        //     // scrollWheelZoom: false, // Disable scroll wheel zoom
        //     // zoomSnap: 0, // Disable zoom snap
        //     // zoomDelta: 0, // Disable zoom delta
        //     // zoom: 10, // Set initial zoom level
        //     // center: [13.736717, 100.523186], // Set initial center coordinates
        //     // maxBounds: [[13.736717, 100.523186], [13.736717, 100.523186]], // Set max bounds
        // }).setView([13.736717, 100.523186], 10); // Set initial view with center and zoom level













        var map3 = L.map('map3').setView([12.672542, 99.958385], 16);
        map3.removeControl(map3.zoomControl);
        // map3 remove attribution
        map3.removeControl(map3.attributionControl);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
        }).addTo(map3);
        var marker = L.marker([12.672542, 99.958385]).addTo(map3);


        // ---------------------------------------------------------------------
 
    
 </script>
</html>
